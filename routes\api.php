<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AvisController;

// Route pour tester l'authentification
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:api');

// Route pour récupérer les avis d'un conducteur
Route::get('/avis/conducteur/{driver}', [AvisController::class, 'getReviews'])->name('api.driver.reviews');