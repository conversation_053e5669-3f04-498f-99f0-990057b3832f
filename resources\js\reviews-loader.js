/**
 * <PERSON><PERSON><PERSON> le HTML pour les étoiles de notation.
 * @param {number} rating - La note sur 5.
 * @returns {string} - Le HTML des étoiles.
 */
function generateStars(rating) {
    let starsHtml = '';
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;

    for (let i = 0; i < fullStars; i++) {
        starsHtml += '<i class="fas fa-star text-yellow-400"></i>';
    }

    if (hasHalfStar) {
        starsHtml += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
    }

    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<i class="far fa-star text-gray-300"></i>';
    }

    return starsHtml;
}

/**
 * Récupère et affiche les avis pour un conducteur donné.
 * @param {string} driverId - L'ID du conducteur.
 * @param {HTMLElement} container - L'élément conteneur où afficher les avis.
 */
window.fetchAndDisplayReviews = function(driverId, container) {
    console.log('fetchAndDisplayReviews appelée pour driverId:', driverId); // DEBUG

    if (!driverId || !container) {
        console.error('Driver ID ou conteneur manquant.'); // DEBUG
        return;
    }

    // Affiche le loader
    container.innerHTML = `
        <div class="text-center text-gray-500 py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
            <p class="mt-4">Chargement des avis...</p>
        </div>`;

    console.log(`Appel API vers: /api/avis/conducteur/${driverId}`); // DEBUG

    fetch(`/api/avis/conducteur/${driverId}`)
        .then(response => {
            console.log('Réponse API reçue. Statut:', response.status); // DEBUG
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(reviews => {
            console.log('Données des avis (JSON):', reviews); // DEBUG
            if (reviews.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-comment-slash text-4xl mb-4"></i>
                        <p>Aucun avis pour le moment</p>
                    </div>`;
                return;
            }

            let reviewsHtml = '';
            reviews.forEach(review => {
                const reviewDate = new Date(review.date).toLocaleDateString('fr-FR');
                const stars = generateStars(review.note);

                reviewsHtml += `
                    <div class="review-card bg-gray-50 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3"> 
                                     ${review.user.photo ? 
                                        `<img src="data:${review.user.phototype};base64,${review.user.photo}" alt="${review.user.name}" class="w-10 h-10 rounded-full object-cover">` : 
                                        '<i class="fas fa-user text-gray-500"></i>'
                                    }
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800">${review.user.name}</p>
                                    <div class="flex items-center">
                                        ${stars}
                                        <span class="ml-2 text-sm font-semibold text-gray-700">${review.note}/5</span>
                                    </div>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">${reviewDate}</span>
                        </div>
                        ${review.comment ? `<p class="text-gray-600 italic">"${review.comment}"</p>` : ''}
                    </div>`;
            });

            container.innerHTML = reviewsHtml;
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des avis:', error);
            container.innerHTML = '<div class="text-center text-red-500 py-8">Erreur lors du chargement des détails.</div>';
        });
}
