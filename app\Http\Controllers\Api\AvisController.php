<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Satisfaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class AvisController extends Controller
{
    /**
     * Récupère les avis pour un conducteur donné.
     *
     * @param  string $driverId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews($driverId)
    {
        try {
            $driver = User::find($driverId);

            if (!$driver) {
                return response()->json(['error' => 'Conducteur non trouvé.'], 404);
            }

            $covoiturageIds = $driver->covoiturages()->pluck('covoit_id');

            $reviews = Satisfaction::whereIn('covoit_id', $covoiturageIds)
                ->with('user:user_id,name,photo,phototype')
                ->latest('date')
                ->get();

            $cleanedReviews = $reviews->map(function ($review) {
                if ($review->comment) {
                    $review->comment = mb_convert_encoding($review->comment, 'UTF-8', 'UTF-8');
                }
                if ($review->review) {
                    $review->review = mb_convert_encoding($review->review, 'UTF-8', 'UTF-8');
                }
                if ($review->user && $review->user->name) {
                    $review->user->name = mb_convert_encoding($review->user->name, 'UTF-8', 'UTF-8');
                }
                return $review;
            });

            return response()->json($cleanedReviews);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des avis pour le conducteur ' . $driverId . ': ' . $e->getMessage());
            return response()->json(['error' => 'Impossible de récupérer les avis.'], 500);
        }
    }
}
