<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Satisfaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class AvisController extends Controller
{
    /**
     * Récupère les avis pour un conducteur donné.
     *
     * @param  string $driverId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews($driverId)
    {
        try {
            $driver = User::find($driverId);

            if (!$driver) {
                return response()->json(['error' => 'Conducteur non trouvé.'], 404);
            }

            $covoiturageIds = $driver->covoiturages()->pluck('covoit_id');

            if ($covoiturageIds->isEmpty()) {
                return response()->json([]);
            }

            $reviews = Satisfaction::whereIn('covoit_id', $covoiturageIds)
                ->with('user:user_id,name,photo,phototype')
                ->latest('date')
                ->get();

            $cleanedReviews = [];

            foreach ($reviews as $review) {
                try {
                    $cleanedReview = [
                        'satisfaction_id' => $review->satisfaction_id,
                        'user_id' => $review->user_id,
                        'covoit_id' => $review->covoit_id,
                        'feeling' => $review->feeling,
                        'note' => $review->note,
                        'date' => $review->date,
                        'comment' => $review->comment ? $this->cleanText($review->comment) : null,
                        'review' => $review->review ? $this->cleanText($review->review) : null,
                        'user' => null
                    ];

                    // Gestion sécurisée des données utilisateur
                    if ($review->user) {
                        $cleanedReview['user'] = [
                            'user_id' => $review->user->user_id,
                            'name' => $review->user->name ? $this->cleanText($review->user->name) : 'Utilisateur',
                            'photo' => $review->user->photo,
                            'phototype' => $review->user->phototype
                        ];
                    }

                    $cleanedReviews[] = $cleanedReview;
                } catch (\Exception $reviewError) {
                    Log::warning('Erreur lors du traitement d\'un avis (ID: ' . $review->satisfaction_id . '): ' . $reviewError->getMessage());
                    // On continue avec les autres avis
                    continue;
                }
            }

            return response()->json($cleanedReviews);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des avis pour le conducteur ' . $driverId . ': ' . $e->getMessage());
            return response()->json(['error' => 'Impossible de récupérer les avis.'], 500);
        }
    }

    /**
     * Nettoie et valide le texte pour l'encodage UTF-8
     */
    private function cleanText($text)
    {
        if (empty($text)) {
            return $text;
        }

        try {
            // Supprimer les caractères de contrôle problématiques
            $text = preg_replace('/[\r\n]+/', ' ', $text);

            // Nettoyer les caractères non-UTF8
            $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

            // Supprimer les caractères de contrôle restants
            $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

            // Nettoyer les espaces multiples
            $text = preg_replace('/\s+/', ' ', $text);

            return trim($text);
        } catch (\Exception $e) {
            Log::warning('Erreur lors du nettoyage du texte: ' . $e->getMessage());
            return 'Texte non disponible';
        }
    }
}
