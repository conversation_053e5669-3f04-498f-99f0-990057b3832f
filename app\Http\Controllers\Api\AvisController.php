<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Satisfaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class AvisController extends Controller
{
    /**
     * Récupère les avis pour un conducteur donné.
     *
     * @param  string $driverId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews($driverId)
    {
        try {
            $driver = User::find($driverId);

            if (!$driver) {
                return response()->json(['error' => 'Conducteur non trouvé.'], 404);
            }

            $covoiturageIds = $driver->covoiturages()->pluck('covoit_id');

            $reviews = Satisfaction::whereIn('covoit_id', $covoiturageIds)
                ->with('user:user_id,name,photo,phototype')
                ->latest('date')
                ->get();

            $cleanedReviews = $reviews->map(function ($review) {
                // Nettoyage et validation UTF-8 pour les commentaires
                if ($review->comment) {
                    $review->comment = $this->cleanText($review->comment);
                }

                // Nettoyage et validation UTF-8 pour les avis
                if ($review->review) {
                    $review->review = $this->cleanText($review->review);
                }

                // Nettoyage du nom d'utilisateur
                if ($review->user && $review->user->name) {
                    $review->user->name = $this->cleanText($review->user->name);
                }

                return $review;
            });

            return response()->json($cleanedReviews);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des avis pour le conducteur ' . $driverId . ': ' . $e->getMessage());
            return response()->json(['error' => 'Impossible de récupérer les avis.'], 500);
        }
    }

    /**
     * Nettoie et valide le texte pour l'encodage UTF-8
     */
    private function cleanText($text)
    {
        if (empty($text)) {
            return $text;
        }

        try {
            // Supprimer les caractères de contrôle problématiques
            $text = preg_replace('/[\r\n]+/', ' ', $text);

            // Nettoyer les caractères non-UTF8
            $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

            // Supprimer les caractères de contrôle restants
            $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

            // Nettoyer les espaces multiples
            $text = preg_replace('/\s+/', ' ', $text);

            return trim($text);
        } catch (\Exception $e) {
            Log::warning('Erreur lors du nettoyage du texte: ' . $e->getMessage());
            return 'Texte non disponible';
        }
    }
}
