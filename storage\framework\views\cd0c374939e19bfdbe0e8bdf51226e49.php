<div x-show="open" x-cloak x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-x-full"
    x-transition:enter-end="opacity-100 transform translate-x-0" x-transition:leave="transition ease-in duration-300"
    x-transition:leave-start="opacity-100 transform translate-x-0"
    x-transition:leave-end="opacity-0 transform translate-x-full" @click.away="open = false"
    class="fixed right-0 bg-green-500 text-white md:hidden p-4 z-40 mt-16">
    <a href="<?php echo e(route('welcome')); ?>"
        class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">Accueil</a>
    <a href="<?php echo e(route('covoiturage')); ?>"
        class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">Covoiturage</a>
    <a href="<?php echo e(route('contact')); ?>"
        class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">Contact</a>
    <hr class="my-2 border-green-400" />
    <?php if(auth()->guard()->guest()): ?>
        <a href="<?php echo e(route('login')); ?>"
            class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">Se
            connecter</a>
        <a href="<?php echo e(route('register')); ?>"
            class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">S'enregistrer</a>
    <?php endif; ?>
    <?php if(auth()->guard()->check()): ?>
        <a href="<?php echo e(route('dashboard')); ?>"
            class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">
            <?php if(auth()->user()->isAdmin()): ?>
                ADMIN
            <?php else: ?>
                <?php echo e(auth()->user()->name); ?>

            <?php endif; ?>
        </a>
        <form method="POST" action="<?php echo e(route('logout')); ?>">
            <?php echo csrf_field(); ?>
            <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); this.closest('form').submit();"
                class="block py-2 px-4 text-sm hover:bg-green-600 hover:font-bold hover:shadow-lg transition duration-300 ease-in-out">
                Déconnexion
            </a>
        </form>
    <?php endif; ?>
</div>
<?php /**PATH /var/www/html/resources/views/layouts/partials/mobile-menu.blade.php ENDPATH**/ ?>