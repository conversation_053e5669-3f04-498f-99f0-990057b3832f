<?php return array (
  'laravel-lang/actions' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Actions\\ServiceProvider',
    ),
  ),
  'laravel-lang/attributes' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Attributes\\ServiceProvider',
    ),
  ),
  'laravel-lang/config' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Config\\ServiceProvider',
    ),
  ),
  'laravel-lang/http-statuses' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\HttpStatuses\\ServiceProvider',
    ),
  ),
  'laravel-lang/lang' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Lang\\ServiceProvider',
    ),
  ),
  'laravel-lang/locales' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Locales\\ServiceProvider',
    ),
  ),
  'laravel-lang/models' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Models\\ServiceProvider',
    ),
  ),
  'laravel-lang/moonshine' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\MoonShine\\ServiceProvider',
    ),
  ),
  'laravel-lang/publisher' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Publisher\\ServiceProvider',
    ),
  ),
  'laravel-lang/routes' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Routes\\ServiceProvider',
    ),
  ),
  'laravel-lang/starter-kits' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\StarterKits\\ServiceProvider',
    ),
  ),
  'laravel/breeze' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Breeze\\BreezeServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
);